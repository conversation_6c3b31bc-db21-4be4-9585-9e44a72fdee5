<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\BusinessSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if profit margin business model setting exists
        $profitMarginSetting = \DB::table('business_settings')->where('key', 'profit_margin_business_model')->first();
        if (!$profitMarginSetting) {
            $maxId = \DB::table('business_settings')->max('id') ?? 0;
            \DB::table('business_settings')->insert([
                'id' => $maxId + 1,
                'key' => 'profit_margin_business_model',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        // Update commission business model setting
        \DB::table('business_settings')->where('key', 'commission_business_model')->update([
            'value' => '0',
            'updated_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        \DB::table('business_settings')->where('key', 'profit_margin_business_model')->delete();

        // Re-enable commission business model
        \DB::table('business_settings')->updateOrInsert(
            ['key' => 'commission_business_model'],
            ['key' => 'commission_business_model', 'value' => '1', 'created_at' => now(), 'updated_at' => now()]
        );
    }
};
