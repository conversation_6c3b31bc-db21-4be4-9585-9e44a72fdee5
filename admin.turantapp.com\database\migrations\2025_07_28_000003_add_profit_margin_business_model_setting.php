<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\BusinessSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add business setting for profit margin model
        BusinessSetting::updateOrCreate(
            ['key' => 'profit_margin_business_model'],
            ['value' => '1']
        );
        
        // Disable commission business model when profit margin is enabled
        BusinessSetting::updateOrCreate(
            ['key' => 'commission_business_model'],
            ['value' => '0']
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        BusinessSetting::where('key', 'profit_margin_business_model')->delete();
        
        // Re-enable commission business model
        BusinessSetting::updateOrCreate(
            ['key' => 'commission_business_model'],
            ['value' => '1']
        );
    }
};
